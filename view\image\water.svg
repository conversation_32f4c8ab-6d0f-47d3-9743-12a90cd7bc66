<svg width="100%" height="100%" id="svg" viewBox="0 0 1440 600" xmlns="http://www.w3.org/2000/svg" class="transition duration-300 ease-in-out delay-150">
    <defs>
        <linearGradient id="gradient" x1="0%" y1="50%" x2="100%" y2="50%">
        <stop offset="5%" stop-color="#8ed1fc"></stop>
        <stop offset="95%" stop-color="#32ded4"></stop>
        </linearGradient>
    </defs>
    <path d="M 0,600 C 0,600 0,200 0,200 C 98.26410256410256,162.3948717948718 196.52820512820512,124.78974358974361 263,131 C 329.4717948717949,137.2102564102564 364.151282051282,187.23589743589744 444,194 C 523.848717948718,200.76410256410256 648.8666666666666,164.26666666666668 741,151 C 833.1333333333334,137.73333333333332 892.3820512820512,147.6974358974359 974,149 C 1055.6179487179488,150.3025641025641 1159.6051282051283,142.94358974358974 1241,150 C 1322.3948717948717,157.05641025641026 1381.1974358974358,178.52820512820512 1440,200 C 1440,200 1440,600 1440,600 Z"
          stroke="none" stroke-width="0" fill="url(#gradient)" fill-opacity="0.53" class="transition-all duration-300 ease-in-out delay-150 path-0">
    </path>
    <defs>
        <linearGradient id="gradient" x1="0%" y1="50%" x2="100%" y2="50%">
            <stop offset="5%" stop-color="#8ed1fc"></stop>
            <stop offset="95%" stop-color="#32ded4"></stop>
        </linearGradient>
    </defs>
    <path d="M 0,600 C 0,600 0,400 0,400 C 78.32307692307694,434.45384615384614 156.64615384615388,468.9076923076923 230,447 C 303.3538461538461,425.0923076923077 371.7384615384615,346.82307692307694 450,346 C 528.2615384615385,345.17692307692306 616.4,421.8 713,423 C 809.6,424.2 914.6615384615386,349.976923076923 990,343 C 1065.3384615384614,336.023076923077 1110.9538461538461,396.2923076923077 1181,417 C 1251.0461538461539,437.7076923076923 1345.523076923077,418.8538461538461 1440,400 C 1440,400 1440,600 1440,600 Z"
          stroke="none" stroke-width="0" fill="url(#gradient)" fill-opacity="1" class="transition-all duration-300 ease-in-out delay-150 path-1">
    </path>
</svg>
