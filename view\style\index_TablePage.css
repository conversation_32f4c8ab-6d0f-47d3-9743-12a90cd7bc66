.tableDistributionBox {
  /* background-image: linear-gradient(120deg, #fdfbfb 0%, #e<PERSON>ee 100%); */
  /* height: 100%;
  display: flex;
  flex-direction: column; */
  position: fixed;
  top: 0;
  left: 0;
  bottom: 0;
  right: 0;
  background-image: linear-gradient(to top, #1e3c72 0%, #1e3c72 1%, #2a5298 100%);
  display: flex;
  flex-direction: column;
}
.table-grid-container {
  border-top-right-radius: 0.6rem;
  background: #fff;
  margin-top: 0.7rem;
  padding: 0.5rem 0.5rem;
  box-sizing: border-box;
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: auto;
}
.table-grid-header {
  display: flex;
  /* justify-content: space-between; */
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.5rem;
  color: #273e5f;
}
.table-grid-header-title {
  font-size: 0.7rem;
  color: #273e5f;
}
.table-grid-header-right {
  display: flex;
  align-items: center;
}
.table-grid-header-right .tableNum-jump {
  display: flex;
  margin-right: 0.9rem;
  align-items: center;
}
.table-grid-header-right .tableNum-jump .layui-input {
  width: 3rem;
  border-top: 0;
  border-right: 0;
  border-left: 0;
  border-bottom: 1px solid rgb(118, 118, 118);
  border-radius: unset;
  color: rgba(0, 0, 0, 0.87);
  font-size: 0.37rem;
}
.table-grid-header-right .tableNum-jump .layui-input:focus {
  border-color: #273e5f !important;
  font-size: 0.37rem !important;
}
.table-grid-header-right .tableNum-jump .layui-input::-webkit-input-placeholder {
  font-size: 0.37rem;
  transition: all 0.3s;
  -webkit-transition: all 0.3s;
  -moz-transition: all 0.3s;
  -ms-transition: all 0.3s;
}
.table-grid-header-right .tableNum-jump .layui-input:focus::-webkit-input-placeholder {
  font-size: 0.3rem;
}
.table-grid-header-right .tableNum-jump .layui-icon {
  font-size: 0.55rem;
  margin-left: 0.2rem;
}

.table-grid-header-icon {
  font-size: 0.55rem !important;
  color: #5593fb;
}

.table-grid-cell {
  overflow-y: auto;
  display: grid;
  grid-template-columns: repeat(3, minmax(0, 1fr));
  gap: 0.3rem;
  overflow-x: hidden;
  flex: 1;
}
.table-card {
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  align-items: flex-start;
  padding: 0.2rem 0.2rem;
  min-height: 2.5rem;
  border-radius: 0.2rem;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.table-number {
  font-weight: bold;
  font-size: 0.8rem;
}

.table-info {
  display: flex;
  justify-content: space-between;
  align-items: flex-end;
  /* width: 100%; */
}

.table-time {
  color: #777;
}

.table-guests {
  color: #777;
}

.open {
  background-color: #3dc13d;
}

/* .occupied {
  background-color: #9ccc65;
} */
/* .occupied {
  background-color: #ffd54f;
} */

.unopened {
  background-color: #3e42ba;
}

.closed {
  background-color: #bdbdbd;
}
/* .closed {
  background-color: #757575;
} */

.open .table-number,
.occupied .table-number,
.unopened .table-number,
.closed .table-number {
  color: #fff;
}

.open .table-info,
.occupied .table-info,
.unopened .table-info,
.closed .table-info {
  color: #fff;
}

@keyframes circle-in-center {
  from {
    clip-path: circle(0%);
  }
  to {
    clip-path: circle(125%);
  }
}

[transition-style="in:circle:center"] {
  animation: 2.5s cubic-bezier(0.25, 1, 0.3, 1) circle-in-center both;
}
@keyframes circle-out-hesitate {
  0% {
    clip-path: circle(125%);
  }
  40% {
    clip-path: circle(40%);
  }
  100% {
    clip-path: circle(0%);
  }
}

[transition-style="out:circle:hesitate"] {
  animation: 2.5s cubic-bezier(0.25, 1, 0.3, 1) circle-out-hesitate both;
}
