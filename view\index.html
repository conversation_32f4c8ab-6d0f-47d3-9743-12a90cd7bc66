<!DOCTYPE html>
<html lang="en" data-page="index">
  <head>
    <meta charset="UTF-8" />
    <meta
      content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no"
      name="viewport"
    />
    <link href="./style/normalize.css" rel="stylesheet" />
    <link href="./style/index.css" rel="stylesheet" />
    <link href="./style/maskLayer.css" rel="stylesheet" />
    <link href="./static/css/page/member-center.css" rel="stylesheet" />
    <link href="./style/index_TablePage.css" rel="stylesheet" />
    <script src="./static/vue/vue.min.js"></script>
    <script src="./static/js/index/utils.js"></script>
    <!--    jQuery-->
    <script src="./static/plugins/jQuery/jquery-3.6.0.min.js"></script>
    <!--    下拉列表-->
    <link
      href="./static/lanSelect/jquery.sweet-dropdown.min.css"
      rel="stylesheet"
      type="text/css"
    />
    <script src="./static/lanSelect/jquery.sweet-dropdown.min.js"></script>
    <!--    layui-->
    <link href="./static/layui/css/layui.css" rel="stylesheet" />
    <script src="./static/layui/layui.js"></script>
    <!--  flexible  -->
    <script pc-del src="./static/js/page/lib-flexible.js"></script>
    <!--    i18n多语言-->
    <script src="./static/I18n/initI18n.js"></script>
    <!--    openTable请求接口-->
    <script src="./static/js/index/useOpenTableInterface.js"></script>
    <!-- js-cookie -->
    <script src="./static/js-cookie/index.js"></script>
    <!--    moment-->
    <script src="./static/moment/moment.js"></script>
    <!--    mock-->
    <!--    <script src="./static/mock.js"></script>-->
    <link rel="stylesheet" href="./static/plugins/transition-style/transition.min.css" />
    <link rel="stylesheet" href="./static/vendor/animate.min.css" />
    <!--     version -->
    <script src="./static/js/versionTag.js"></script>
    <script src="./static/byod_webUtils/public.js"></script>
    <script src="./static/components/DiningStyleDialog.js"></script>
    <!--  国旗图标  -->
    <link href="./static/vendor/flagIcons/flag-icons.css" rel="stylesheet" />
    <script src="./static/js/page/accountMixin.js"></script>
    <script src="./static/js/page/globalStoreMixin.js"></script>
    <script src="./static/js/page/registerAndResetMixin.js"></script>
    <script src="./static/js/linkTypeDetector.js"></script>

    <link rel="stylesheet" href="./static/css/bottomSheet.css" />
    <link rel="stylesheet" href="./static/css/couponPage.css" />
    <link rel="stylesheet" href="./style/paxSelector.css" />
  </head>

  <body>
    <div id="app">
      <template v-cloak>
        <version-tag
          :version="urlParams.paramsObj&&urlParams.paramsObj.versionNumber||''"
        ></version-tag>
        <div id="fullbg" style="display: none"></div>
        <div id="dialog">
          <div class="maskimg_warp">
            <!-- 媒体加载状态指示器 -->
            <div v-show="popupMedia.isLoading" class="media-loading-indicator">
              <div class="loading-spinner"></div>
            </div>

            <!-- 右上角广告图关闭区域 -->
            <div class="close_warp" v-show="isPopupCloseAreaVisible">
              <!-- 倒计时显示 -->
              <div
                v-show="!popupCloseControl.isCloseBtnVisible && popupCloseControl.countdown > 0"
                class="countdown-display"
              >
                <div class="countdown-circle">
                  <span class="countdown-text">{{ popupCloseControl.countdown }}</span>
                </div>
              </div>

              <!-- 关闭按钮 -->
              <div
                v-show="popupCloseControl.isCloseBtnVisible"
                @click="closeBg"
                class="close-button"
              >
                <span class="close-icon">×</span>
              </div>
            </div>
            <!-- External URL 媒体内容 -->
            <template v-if="popupMedia.hasExternalUrl">
              <!-- 视频 -->
              <video
                v-if="popupMedia.type === 'video'"
                class="popup-video"
                muted
                autoplay
                preload
                loop
                x5-video-player-fullscreen="true"
                x5-playsinline
                playsinline
                webkit-playsinline
                @click="advertisingMapJump('PopupImage')"
                @loadeddata="onMediaLoaded"
                @canplay="onVideoCanPlay"
              >
                <source :src="popupMedia.url" type="video/mp4" />
              </video>

              <!-- 图片和GIF -->
              <img
                v-show="popupMedia.type === 'img' || popupMedia.type === 'gif'"
                alt=""
                class="popup-image"
                :src="popupMedia.url"
                @click="advertisingMapJump('PopupImage')"
                @load="onMediaLoaded"
                @error="onMediaError"
              />

              <!-- 未知类型链接 -->
              <div
                v-show="popupMedia.type === 'unknown'"
                class="popup-link"
                @click="advertisingMapJump('PopupImage')"
              >
                <div class="link-content">
                  <div class="link-icon">🔗</div>
                </div>
              </div>
            </template>

            <!-- 原始图片内容 -->
            <img
              v-show="!popupMedia.hasExternalUrl"
              alt=""
              class="ad_warp"
              src=""
              @click="advertisingMapJump('PopupImage')"
              @load="onMediaLoaded"
            />
          </div>
        </div>
        <!-- loading蒙层 -->
        <div id="shade"></div>
        <!-- EnhAssistMode显示台号信息 -->
        <div class="tableDistributionBox" v-if="EAMandSMModeSecondPage">
          <div class="table-grid-container">
            <div class="table-grid-header">
              <strong class="table-grid-header-title">{{arrLang.tableDistributionTitle}}</strong>
              <div class="table-grid-header-right">
                <div class="tableNum-jump">
                  <input
                    v-model="jumpTableNum"
                    type="text"
                    name="tableNumber"
                    required
                    :placeholder="arrLang.tableDistributionSearchTBPH"
                    autocomplete="off"
                    class="layui-input"
                  />
                  <i class="layui-icon layui-icon-release" @click="onJumpTableNum"></i>
                </div>
                <i
                  class="layui-icon layui-icon-close table-grid-header-icon"
                  @click="modeLogout"
                ></i>
              </div>
            </div>
            <div
              class="table-grid-cell"
              v-if="tables.length!=0"
              transition-style="in:circle:center"
            >
              <div
                v-for="(table, index) in tables"
                :key="index"
                class="table-card"
                :class="table.tableKey?'open':'unopened'"
                @click="tableClick(table)"
              >
                <div class="table-number">{{ table.tableNumber }}</div>
              </div>
            </div>
          </div>
        </div>

        <section class="ftco-cover" ref="mainDom" id="section-home" v-else>
          <!-- 新版语言切换 -->
          <div
            class="dropdown-menu dropdown-anchor-top-right dropdown-has-anchor menuSelect"
            id="dropdown-standard"
            style="border: 0"
          >
            <ul class="selectUl" style="font-family: Arial, Helvetica, sans-serif">
              <li :key="item.val" v-for="item in lanObj">
                <a @click="chooseLan(item)" class="changeTxt" href="#">
                  {{arrLang[item.label]||item.label}}
                </a>
              </li>
            </ul>
          </div>
          <!-- 语言icon -->
          <div class="new_inSwitch" data-dropdown="#dropdown-standard" v-if="showLanSection">
            Language / 語言
          </div>
          <div
            @click="showUserModelDia"
            class="lbs_logout"
            v-if="initTableNumber==='lbsMode'&&localTableNumber"
          >
            <img alt="login_out" src="./image/logout.svg" />
          </div>
          <div class="container">
            <section class="container_center">
              <div class="content_warp" v-show="currentLan">
                <!-- <p class="content_warp_shopAddr">{{ showShopAddress }}</p>
              <p class="content_warp_tableNum_txt">{{showShopTableNumber()}}</p> -->
                <!-- 改动地址台号放到右下角,div占位不变 -->
                <p class="content_warp_shopAddr"></p>
                <p class="content_warp_tableNum_txt"></p>
              </div>
              <div class="toHomepageBtn">
                <div
                  class="orders_btn_box"
                  :class="{'animate__animated animate__zoomInDown':animatedConfig.toHomePageBtn}"
                  v-if="initBGImg"
                  v-show="!isDiningStyleMode"
                >
                  <!-- goNextPageBtn -->
                  <!-- goToRegister -->
                  <!-- goToResetPassword -->
                  <button
                    @click="goNextPageBtn"
                    class="orders_btn"
                    :class="{'shadow-spread':animatedConfig.toHomePageBtn,'disabledBtn':isDisabledBtn}"
                  >
                    <span class="orders_btnText">{{arrLang.toHomepageBtn}}</span>
                  </button>
                </div>
                <div class="loader loader-3" id="loading">
                  <div class="dot dot1"></div>
                  <div class="dot dot2"></div>
                  <div class="dot dot3"></div>
                </div>
                <!-- 关闭广告图保留缩略图在点餐按钮下 Cover Page-->
                <div class="popupThumbnailBox" @click="advertisingMapJump('PopupThumbnail')">
                  <img
                    alt=""
                    class="popupThumbnailImg"
                    src=""
                    :class="{'animate__animated animate__bounceInLeft':animatedConfig.popupThumbnail}"
                  />
                </div>
              </div>
            </section>

            <template id="template-one" v-if="template==1">
              <div class="template-one">
                <!--  -->
                <div
                  class="back-img-content"
                  v-show="initBGImg"
                  :transition-style="animatedConfig.backgroundImage?'in:circle:top-left':''"
                >
                  <!-- External URL 背景媒体 -->
                  <template v-if="backgroundMedia.hasExternalUrl">
                    <!-- 视频背景 -->
                    <video
                      v-if="backgroundMedia.type === 'video'"
                      class="background-video external-url-video"
                      muted
                      autoplay
                      preload
                      loop
                      x5-video-player-fullscreen="true"
                      x5-playsinline
                      playsinline
                      webkit-playsinline
                    >
                      <source :src="backgroundMedia.url" type="video/mp4" />
                    </video>

                    <!-- GIF和图片背景统一使用backgroundImage方式 -->
                    <div
                      v-else-if="backgroundMedia.type === 'gif' || backgroundMedia.type === 'img'"
                      class="external-url-background"
                      :style="{ backgroundImage: `url(${backgroundMedia.url})` }"
                    ></div>
                  </template>
                </div>
              </div>
            </template>
            <template id="template-two" v-if="template==2">
              <div class="template-two">
                <div
                  class="back-img-content"
                  v-show="initBGImg"
                  :transition-style="animatedConfig.backgroundImage?'in:circle:top-left':''"
                >
                  <div class="top-bcg-warp"></div>
                  <div class="center-info-warp"></div>
                  <div class="bottom-bcg-warp"></div>
                </div>
              </div>
            </template>
            <template id="template-three" v-if="template==3">
              <div class="template-three">
                <div
                  class="back-img-content"
                  v-show="initBGImg"
                  :transition-style="animatedConfig.backgroundImage?'in:circle:top-left':''"
                >
                  <div class="top-bcg-warp"></div>
                  <div class="center-bcg-warp"></div>
                  <div class="bottom-bcg-warp"></div>
                </div>
              </div>
            </template>
            <div class="logo-warp" v-if="initLogoImg">
              <img
                src="#"
                alt="logo"
                class="logo-img"
                :class="{'animate__animated': animatedConfig.companyLogo}"
              />
            </div>
          </div>
          <div class="footnote_warp" v-show="initBGImg">
            <!-- 左边脚注 -->
            <div class="byLeftInfo">
              <p style="white-space: pre-line">{{arrLang.footnote1}}</p>
              <p style="white-space: pre-line">{{arrLang.footnote2}}</p>
            </div>
            <!-- 右边脚注 -->
            <div class="byRightInfo">
              <p class="byRightInfo_shopAddr">
                {{ showShopAddress||initConfigObj.address&&initConfigObj.address[this.currentLan] }}
              </p>
              <!-- v-if="openTableStatus" -->
              <p class="byRightInfo_tableNUm_txt">{{showShopTableNumber}}</p>
              <p class="byRightInfo_tip">{{arrLang.poweredByMessage}}</p>
            </div>
          </div>
        </section>
        <!-- 员工模式输入台号密码 -->
        <div class="userModelDia" style="display: none">
          <div
            class="userModel-form-item"
            v-if="initTableNumber==='StaffMode'&&staffModeUIConfig.loginModel==1"
          >
            <label class="userModel-label">
              <img alt="" class="tableIcon" src="./image/userIcon.jpg" />
            </label>
            <div class="userModel-input-block">
              <label>
                <input
                  :placeholder="arrLang.staffModeCodeText"
                  class="layui-input"
                  name="staffCode"
                  type="text"
                  v-model="loginParams.staffCode"
                />
              </label>
            </div>
          </div>
          <div
            class="userModel-form-item"
            v-if="!['StaffMode','EnhAssistMode'].includes(initTableNumber)"
          >
            <label class="userModel-label">
              <img alt="" class="tableIcon" src="./image/tableIcon.jpg" />
            </label>
            <div class="userModel-input-block">
              <label>
                <input
                  :placeholder="arrLang.staffModelTableNumTxt"
                  class="layui-input"
                  name="tableNum"
                  type="text"
                  v-model="loginParams.tableNumber"
                />
              </label>
            </div>
          </div>
          <div class="userModel-form-item">
            <label class="userModel-label">
              <img alt="" class="passWordIcon" src="./image/passWord.jpg" />
            </label>
            <div class="userModel-input-block">
              <label>
                <input
                  :placeholder="arrLang.staffModelPassWordTxt"
                  class="layui-input"
                  id="passWordDom"
                  name="passWord"
                  type="password"
                  v-model="loginParams.password"
                />
              </label>

              <a
                @click="onPassWordEye"
                class="eye-warp icon-eye-close"
                href="#"
                id="passwordeye"
              ></a>
            </div>
          </div>
          <div class="userModel-form-item">
            <label class="checkboxLabel" v-if="initTableNumber!=='lbsMode'">
              <input id="remPassWord" style="display: none" type="checkbox" />
              <span class="checkbtn"></span>
              <span class="checkboxTxt">{{arrLang.remPassWordTxt}}</span>
            </label>
          </div>
          <div class="userModel-form-item">
            <div class="userModel-form-btn">
              <button
                @click="onAssistModeSub"
                class="layui-btn layui-btn-sm layui-btn-normal subBtn"
                id=""
              >
                {{arrLang.staffModelLoginBtn}}
              </button>
            </div>
          </div>
        </div>
        <!-- 开台弹窗-->
        <div class="openTableDia" style="display: none">
          <div class="userModel-form-item">
            <label class="userModel-label">
              <i class="layui-icon layui-icon-group openTableDiaIcon"></i>
            </label>
            <div class="userModel-input-block">
              <label>
                <input
                  :placeholder="arrLang.paxPlaceholder"
                  class="layui-input"
                  name="tablePax"
                  oninput="value=value.replace(/^(0+)|[^\d]+/g,'')"
                  type="number"
                  v-model.number="pax"
                />
              </label>
            </div>
          </div>
        </div>
        <!-- 店铺超时提示弹窗 -->
        <div class="timeOutPromptDia" style="display: none" v-html="getTimeOutPrompt"></div>

        <!--    选择 用餐方式弹窗       -->
        <dining-style-dialog
          v-if="showDiningStyle"
          @check-dining="onGetDiningCallBack"
          :status="{dineIn,takeaway}"
        ></dining-style-dialog>
        <personal-center
          ref="personalCenter"
          :system-language="menuI18nPkg()"
          :open-Table="openTableData"
          :member-center-logo="memberCenterLogo"
          v-if="openTableData.displayCRM"
        ></personal-center>
        <register-page
          ref="registerPage"
          :system-language="arrLang"
          :open-table="openTableData"
          v-if="showRegister"
        ></register-page>
        <reset-password
          ref="resetPassword"
          :system-language="arrLang"
          :open-table="openTableData"
          v-if="showResetPassword"
        ></reset-password>
        <birthday-card
          style="display: none"
          :system-language="menuI18nPkg()"
          :open-Table="openTableData"
          v-if="openTableData.displayCRM"
        ></birthday-card>
        <order-stop-dia
          ref="orderStopDia"
          :system-language="menuI18nPkg()"
          :open-Table="openTableData"
          :queue-Time="queuedObj.queueTime"
          :store-number="urlParams.paramsObj['storeNumber']"
          :domain="urlParams.companyName"
          current-page="index"
          v-if="queuedObj.isOrderStop"
        ></order-stop-dia>
        <!-- 人数选择器组件 -->
        <pax-selector
          ref="paxSelector"
          :show-pax-selector="showPaxSelector"
          :system-language="arrLang"
          :open-Table="openTableData"
          @close="closePaxSelector"
        ></pax-selector>
      </template>
    </div>

    <script src="./static/js/index/device.js"></script>
    <script src="./static/QRcode/qrcode.min.js" defer></script>
    <script src="./static/plugins/hammer/hammer.min.js" defer></script>
    <script src="./static/components/BottomSheet.js" defer></script>
    <script src="./static/components/Coupons.js" defer></script>
    <script src="./static/components/RechargeView.js" defer></script>
    <script src="./static/components/PersonalCenter.js" defer></script>
    <script src="./static/components/CustomSelect.js" defer></script>
    <script src="./static/components/RegisterPage.js" defer></script>
    <script src="./static/components/ResetPassword.js" defer></script>
    <script src="./static/components/birthdayCard.js" defer></script>
    <script src="./static/components/OrderStopDia.js" defer></script>
    <script src="./static/components/PaxSelector.js" defer></script>
    <script src="./index.js"></script>
  </body>
</html>
