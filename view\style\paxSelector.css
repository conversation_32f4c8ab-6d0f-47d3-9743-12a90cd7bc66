/* ==================== 人数选择器组件样式 ==================== */

.pax-selector-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, rgba(0, 0, 0, 0.4) 0%, rgba(0, 0, 0, 0.6) 100%);
  backdrop-filter: blur(6px);
  -webkit-backdrop-filter: blur(6px);
  z-index: 1000;
  display: flex;
  align-items: center;
  justify-content: center;
  box-sizing: border-box;
}

.pax-selector-container {
  background: rgba(255, 255, 255, 0.92);
  backdrop-filter: blur(12px);
  -webkit-backdrop-filter: blur(12px);
  border: 1px solid rgba(255, 255, 255, 0.3);
  border-radius: 0.35rem;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  width: 85%;
  overflow: hidden;
  animation: paxSelectorSlideIn 0.3s cubic-bezier(0.34, 1.56, 0.64, 1);
  position: relative;
}

@keyframes paxSelectorSlideIn {
  from {
    opacity: 0;
    transform: scale(0.95) translateY(10px);
  }
  to {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
}

.pax-selector-header {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0.5rem 0.3rem;
}

.pax-selector-title {
  margin: 0;
  font-size: 0.4rem;
  font-weight: 600;
  color: #2d3748;
  text-align: center;
  letter-spacing: 0.01em;
}

.pax-selector-content {
  padding: 0 0.5rem 0.3rem;
}

.pax-number-grid {
  display: grid;
  grid-template-columns: repeat(5, 1fr);
  grid-template-rows: repeat(2, 1fr);
  gap: 0.25rem;
  margin-bottom: 0.35rem;
}

.pax-number-btn {
  background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
  border: 1px solid #e2e8f0;
  border-radius: 0.375rem;
  padding: 0.2rem 0.15rem;
  font-size: 0.375rem;
  font-weight: 500;
  color: #4a5568;
  cursor: pointer;
  transition: all 0.25s cubic-bezier(0.4, 0, 0.2, 1);
  height: 0.8rem;
  display: flex;
  align-items: center;
  justify-content: center;
  white-space: nowrap;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  position: relative;
  overflow: hidden;
}

.pax-number-btn::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: color-mix(in srgb, var(--styleColor) 15%, white);
  opacity: 0;
  transition: opacity 0.25s ease;
}

.pax-number-btn span {
  position: relative;
  z-index: 1;
}

/* .pax-number-btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 15px color-mix(in srgb, var(--styleColor) 20%, transparent);
  border-color: color-mix(in srgb, var(--styleColor) 30%, transparent);
}

.pax-number-btn:hover::before {
  opacity: 1;
} */

.pax-number-btn.empty {
  background: transparent;
  border: none;
  cursor: default;
  pointer-events: none;
  box-shadow: none;
}

.pax-number-btn.other {
  font-size: 0.3rem;
  background: color-mix(in srgb, var(--styleColor) 8%, white);
  color: color-mix(in srgb, var(--styleColor) 80%, black);
  border-color: color-mix(in srgb, var(--styleColor) 20%, transparent);
}

/* .pax-number-btn.other:hover {
  box-shadow: 0 4px 15px color-mix(in srgb, var(--styleColor) 20%, transparent);
} */

.pax-number-btn.selected {
  background: var(--styleColor);
  color: white;
  border-color: transparent;
  transform: translateY(-2px) scale(1.03);
  box-shadow: 0 6px 20px color-mix(in srgb, var(--styleColor) 30%, transparent);
  font-weight: 600;
}

.pax-number-btn.selected::before {
  opacity: 0;
}

.pax-other-input-wrapper {
  margin-top: 0.25rem;
  animation: paxInputSlideDown 0.3s cubic-bezier(0.34, 1.56, 0.64, 1);
}

@keyframes paxInputSlideDown {
  from {
    opacity: 0;
    max-height: 0;
    transform: translateY(-5px);
  }
  to {
    opacity: 1;
    max-height: 1.5rem;
    transform: translateY(0);
  }
}

.pax-other-input {
  width: 100%;
  padding: 0.25rem 0.375rem;
  border: 1.5px solid #e2e8f0;
  border-radius: 0.375rem;
  font-size: 0.35rem;
  color: #2d3748;
  background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
  transition: all 0.25s cubic-bezier(0.4, 0, 0.2, 1);
  box-sizing: border-box;
  box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.05);
}

.pax-other-input:focus {
  outline: none;
  border-color: var(--styleColor);
  box-shadow: 0 0 0 2px color-mix(in srgb, var(--styleColor) 20%, transparent),
    inset 0 1px 3px rgba(0, 0, 0, 0.05);
  background: white;
  transform: scale(1.01);
}

.pax-other-input::placeholder {
  color: #718096;
  font-style: italic;
}

.pax-selector-footer {
  padding: 0.2rem 0.5rem 0.5rem;
}

.pax-confirm-btn {
  width: 100%;
  background: var(--styleColor);
  border: none;
  border-radius: 0.5rem;
  padding: 0.3rem 0.75rem;
  font-size: 0.4rem;
  font-weight: 600;
  color: white;
  cursor: pointer;
  transition: all 0.25s cubic-bezier(0.4, 0, 0.2, 1);
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  overflow: hidden;
  box-shadow: 0 3px 12px color-mix(in srgb, var(--styleColor) 30%, transparent);
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.pax-confirm-btn::before {
  content: "";
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.4s ease;
}

/* .pax-confirm-btn:hover {
  transform: translateY(-1px) scale(1.01);
  box-shadow: 0 6px 20px color-mix(in srgb, var(--styleColor) 30%, transparent);
  background: color-mix(in srgb, var(--styleColor) 80%, black);
}

.pax-confirm-btn:hover::before {
  left: 100%;
} */

.pax-confirm-btn.animate {
  animation: buttonPulse 2s ease-in-out infinite, buttonGlow 2.5s ease-in-out infinite alternate;
}

@keyframes buttonPulse {
  0%,
  100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.02);
  }
}

@keyframes buttonGlow {
  0% {
    box-shadow: 0 3px 12px color-mix(in srgb, var(--styleColor) 30%, transparent);
  }
  100% {
    box-shadow: 0 5px 20px color-mix(in srgb, var(--styleColor) 30%, transparent);
  }
}

.pax-confirm-btn:active:not(.disabled) {
  transform: translateY(0) scale(0.98);
  transition: transform 0.1s ease;
}

.pax-confirm-btn.disabled {
  background: linear-gradient(135deg, #e2e8f0 0%, #cbd5e0 100%);
  color: #718096;
  cursor: not-allowed;
  animation: none;
  transform: none;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  text-shadow: none;
}

.pax-confirm-btn.disabled::before {
  display: none;
}

/* 只为iPhone 6/7/8优化 */
@media only screen and (device-width: 375px) and (device-height: 667px) and (-webkit-device-pixel-ratio: 2) {
  .pax-selector-container {
    width: 70%;
    max-width: 15rem;
  }

  .pax-selector-header {
    padding: 0.5rem 0.6rem 0.3rem;
  }

  .pax-selector-title {
    font-size: 0.375rem;
  }

  .pax-selector-content {
    padding: 0 0.6rem 0.4rem;
  }

  .pax-number-grid {
    gap: 0.2rem;
    margin-bottom: 0.3rem;
  }

  .pax-number-btn {
    padding: 0.15rem 0.1rem;
    font-size: 0.32rem;
    height: 0.7rem;
    border-radius: 0.3rem;
  }

  .pax-other-input {
    padding: 0.2rem 0.3rem;
    font-size: 0.3rem;
    border-radius: 0.3rem;
  }

  .pax-confirm-btn {
    padding: 0.4rem 0.6rem;
    font-size: 0.35rem;
    border-radius: 0.4rem;
  }
}
